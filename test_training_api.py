"""
测试训练API
"""
import asyncio
import json
import requests
import time

async def test_training_api():
    """测试训练API"""
    print("开始测试训练API...")
    
    # API基础URL
    base_url = "http://localhost:8000/api/v1"
    
    # 测试数据集ID
    dataset_id = "test_dataset_001"
    
    try:
        # 1. 测试健康检查API
        print("\n1. 测试健康检查API")
        try:
            response = requests.get(f"{base_url}/health")
            response.raise_for_status()
            print(f"健康检查成功: {json.dumps(response.json(), indent=2)}")
        except Exception as e:
            print(f"健康检查失败: {str(e)}")
        
        # 2. 测试数据集验证API
        print("\n2. 测试数据集验证API")
        try:
            response = requests.get(f"{base_url}/datasets/{dataset_id}/validate")
            response.raise_for_status()
            print(f"数据集验证成功: {json.dumps(response.json(), indent=2)}")
        except Exception as e:
            print(f"数据集验证失败: {str(e)}")
            print("使用本地数据进行后续测试...")
        
        # 3. 测试训练API（同步模式）
        print("\n3. 测试训练API（同步模式）")
        try:
            training_request = {
                "training_set_id": dataset_id,
                "parameters": {
                    "target_column": "label",
                    "test_size": 0.3,
                    "random_state": 42
                }
            }
            
            response = requests.post(
                f"{base_url}/train",
                json=training_request
            )
            response.raise_for_status()
            print(f"训练请求成功: {json.dumps(response.json(), indent=2)}")
            
            # 获取模型ID
            model_id = response.json().get("model_id")
            if model_id and model_id != "pending":
                print(f"模型ID: {model_id}")
            else:
                print("未获取到有效的模型ID")
                model_id = None
        except Exception as e:
            print(f"训练请求失败: {str(e)}")
            model_id = None
        
        # 4. 测试训练API（异步模式）
        print("\n4. 测试训练API（异步模式）")
        try:
            training_request = {
                "training_set_id": dataset_id,
                "parameters": {
                    "target_column": "label",
                    "test_size": 0.3,
                    "random_state": 42
                },
                "callback_url": "http://localhost:8000/api/v1/callback"  # 模拟回调URL
            }
            
            response = requests.post(
                f"{base_url}/train?async_mode=true",
                json=training_request
            )
            response.raise_for_status()
            print(f"异步训练请求成功: {json.dumps(response.json(), indent=2)}")
        except Exception as e:
            print(f"异步训练请求失败: {str(e)}")
        
        # 5. 测试获取训练任务状态API
        print("\n5. 测试获取训练任务状态API")
        try:
            response = requests.get(f"{base_url}/training-tasks")
            response.raise_for_status()
            print(f"获取训练任务状态成功: {json.dumps(response.json(), indent=2)}")
        except Exception as e:
            print(f"获取训练任务状态失败: {str(e)}")
        
        # 6. 测试获取模型列表API
        print("\n6. 测试获取模型列表API")
        try:
            response = requests.get(f"{base_url}/models")
            response.raise_for_status()
            print(f"获取模型列表成功: {json.dumps(response.json(), indent=2)}")
        except Exception as e:
            print(f"获取模型列表失败: {str(e)}")
        
        # 7. 测试获取模型详情API（如果有模型ID）
        if model_id:
            print(f"\n7. 测试获取模型详情API: {model_id}")
            try:
                response = requests.get(f"{base_url}/models/{model_id}")
                response.raise_for_status()
                print(f"获取模型详情成功: {json.dumps(response.json(), indent=2)}")
            except Exception as e:
                print(f"获取模型详情失败: {str(e)}")
        
        print("\n测试完成!")
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")

if __name__ == "__main__":
    # 等待服务启动
    print("等待服务启动...")
    time.sleep(2)
    
    asyncio.run(test_training_api())