# 脚本工具目录

本目录包含企业风险评估项目的各种工具脚本，用于数据生成、分析、评估和验证。

## 📁 目录结构

```
scripts/
├── data/                          # 数据相关脚本
│   ├── data_sample_generate.py    # 生成样本数据
│   ├── comprehensive_data_quality_assessment.py  # 全面数据质量评估
│   ├── feature_engineering_enhancement.py        # 特征工程增强
│   └── data_analysis_utils.py     # 数据分析工具函数
├── run_pipeline.py                # 主要训练流水线
├── run_enhanced_pipeline.py       # 增强特征训练流水线
├── model_baseline_comparison.py   # 基于baseline的统一对比分析
├── model_compare_traditional.py   # 传统方法对比分析
├── model_analyze_golden_samples.py # 黄金样本分析
├── model_monitor.py               # 模型监控
├── export_scorecard_rules.py      # 评分卡规则导出
└── README.md                      # 本说明文档
```

## 🚀 脚本使用指南

### 1. 数据生成与质量评估
```bash
# 生成样本数据
python scripts/data/data_sample_generate.py

# 全面数据质量评估（包含IV值分析）
python scripts/data/comprehensive_data_quality_assessment.py

# 特征工程增强
python scripts/data/feature_engineering_enhancement.py
```

### 2. 模型训练流水线
```bash
# 标准训练流水线
python scripts/run_pipeline.py

# 增强特征训练流水线（包含特征交互）
python scripts/run_enhanced_pipeline.py
```

### 3. 模型对比与分析
```bash
# 基于baseline的统一对比分析（包含PSI漂移检测）
python scripts/model_baseline_comparison.py

# 传统方法详细对比分析
python scripts/model_compare_traditional.py

# 黄金样本深度分析
python scripts/model_analyze_golden_samples.py
```

### 4. 模型监控与部署
```bash
# 模型性能监控
python scripts/model_monitor.py

# 导出评分卡规则为JSON格式
python scripts/export_scorecard_rules.py
```

## 📋 脚本功能说明

### 数据相关脚本

#### `data/data_sample_generate.py`
- **功能**：生成企业风险评估样本数据
- **输出**：`data/enterprise_risk_sample_data.csv`
- **特点**：基于真实指标体系，支持不同区分度等级的指标生成

#### `data/comprehensive_data_quality_assessment.py`
- **功能**：全面数据质量评估
- **内容**：IV值计算、特征重要性评估、数据质量综合评分
- **适用场景**：数据质量监控、特征工程指导

#### `data/feature_engineering_enhancement.py`
- **功能**：特征工程增强
- **内容**：特征交互、趋势分析、高价值特征生成
- **适用场景**：模型性能提升、特征优化

### 训练流水线脚本

#### `run_pipeline.py`
- **功能**：标准WOE分箱+逻辑回归训练流水线
- **输出**：完整的模型文件和评估结果
- **适用场景**：标准模型训练、基线建立

#### `run_enhanced_pipeline.py`
- **功能**：增强特征训练流水线
- **特点**：包含特征交互和增强特征
- **适用场景**：模型性能优化、高级特征应用

### 模型分析脚本

#### `model_baseline_comparison.py`
- **功能**：基于baseline的统一对比分析
- **特点**：包含PSI漂移检测、版本演进分析
- **输出**：综合对比报告、漂移检测结果

#### `model_compare_traditional.py`
- **功能**：模型与传统方法详细对比
- **内容**：ROC曲线对比、性能指标分析
- **适用场景**：模型效果验证、业务解释

#### `model_analyze_golden_samples.py`
- **功能**：黄金样本深度分析
- **内容**：关键样本识别、预测差异分析
- **适用场景**：模型调优、异常样本分析

### 监控与部署脚本

#### `model_monitor.py`
- **功能**：模型性能监控
- **内容**：性能指标跟踪、预警机制
- **适用场景**：生产环境监控、模型维护

#### `export_scorecard_rules.py`
- **功能**：评分卡规则导出
- **输出**：JSON格式的规则文件
- **适用场景**：生产部署、规则引擎集成

## 💡 使用建议

### 开发阶段工作流
1. **数据准备**：使用 `data_sample_generate.py` 生成样本数据
2. **质量评估**：用 `comprehensive_data_quality_assessment.py` 评估数据质量
3. **模型训练**：运行 `run_pipeline.py` 建立基线模型
4. **性能优化**：使用 `run_enhanced_pipeline.py` 训练增强版本

### 生产监控流程
1. **定期对比**：使用 `model_baseline_comparison.py` 检测数据漂移
2. **性能监控**：运行 `model_monitor.py` 跟踪模型表现
3. **深度分析**：用 `model_analyze_golden_samples.py` 分析关键样本
4. **规则更新**：通过 `export_scorecard_rules.py` 导出最新规则

### 版本发布前检查
```bash
# 1. 数据漂移检测
python scripts/model_baseline_comparison.py --type version

# 2. 详细性能对比
python scripts/model_compare_traditional.py

# 3. 黄金样本验证
python scripts/model_analyze_golden_samples.py
```

## ⚠️ 注意事项

- 所有脚本都假设在项目根目录下运行
- 确保虚拟环境已激活：`source .venv/bin/activate`
- 数据文件路径基于项目根目录的相对路径
- 运行前确保 `data/` 目录存在

## 🔧 依赖要求

脚本依赖项目的主要依赖包：
- pandas
- numpy  
- scikit-learn
- matplotlib
- seaborn
- optbinning

安装依赖：
```bash
pip install -e .
```

## 📊 输出文件说明

### 模型输出
- `outputs/<version>/models/` - 训练好的模型文件
- `outputs/<version>/feature/` - 特征工程结果
- `outputs/<version>/evaluation/` - 评估结果和报告

### 对比分析输出
- `outputs/<version>/baseline/` - baseline数据和对比结果
- `outputs/baseline_comparison_report_*.md` - 综合对比报告
- `outputs/*_roc_comparison.png` - ROC曲线对比图

### 规则导出
- `outputs/<version>/scorecard_rules.json` - 评分卡规则文件
- `outputs/<version>/scorecard.md` - 评分卡说明文档
