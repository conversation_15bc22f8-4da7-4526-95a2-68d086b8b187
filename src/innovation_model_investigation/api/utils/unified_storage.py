"""
统一模型存储管理，支持本地和RustFS双重存储
"""
import os
import shutil
import json
from pathlib import Path
from typing import Dict, Any, Optional, List
from loguru import logger

try:
    from innovation_model_investigation.api.utils.rustfs_storage import RustFSModelStorage
    RUSTFS_AVAILABLE = True
except ImportError:
    logger.warning("RustFS存储模块不可用")
    RUSTFS_AVAILABLE = False


class UnifiedModelStorage:
    """统一模型存储，确保同时保存到本地和RustFS"""
    
    def __init__(self, base_dir: str = "outputs", use_rustfs: bool = True):
        """
        初始化统一存储
        
        Args:
            base_dir: 基础存储目录
            use_rustfs: 是否启用RustFS存储
        """
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(parents=True, exist_ok=True)
        self.use_rustfs = use_rustfs and RUSTFS_AVAILABLE
        
        # 初始化RustFS存储
        if self.use_rustfs:
            try:
                self.rustfs_storage = RustFSModelStorage(base_dir=str(self.base_dir))
                logger.info("✅ RustFS存储初始化成功")
            except Exception as e:
                logger.error(f"❌ RustFS存储初始化失败: {str(e)}")
                self.use_rustfs = False
                self.rustfs_storage = None
        else:
            self.rustfs_storage = None
    
    def save_model_version(
        self, 
        version: str, 
        files: Dict[str, Path], 
        metadata: Dict[str, Any]
    ) -> str:
        """
        保存模型版本到本地和RustFS
        
        Args:
            version: 版本号
            files: 文件路径字典
            metadata: 元数据
            
        Returns:
            版本目录路径
        """
        logger.info(f"💾 开始保存模型版本: {version}")
        logger.info(f"📁 待保存文件: {list(files.keys())}")
        
        try:
            # 1. 确保本地版本目录存在
            version_dir = self.base_dir / version
            version_dir.mkdir(parents=True, exist_ok=True)
            
            # 2. 保存元数据到本地
            metadata_path = version_dir / "metadata.json"
            with open(metadata_path, "w", encoding="utf-8") as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            logger.info(f"📄 本地元数据已保存: {metadata_path}")
            
            # 3. 强制保存到RustFS（如果启用）
            if self.use_rustfs and self.rustfs_storage:
                logger.info("☁️ 开始保存到RustFS...")
                
                try:
                    # 删除已存在的版本
                    try:
                        existing_info = self.rustfs_storage.get_model_info(version)
                        if existing_info:
                            logger.info(f"🗑️ 删除RustFS中已存在的版本: {version}")
                            self.rustfs_storage.delete_model(version)
                    except:
                        pass
                    
                    # 保存到RustFS
                    rustfs_path = self.rustfs_storage.save_model(
                        model_id=version,
                        version=version,
                        files=files,
                        metadata=metadata
                    )
                    logger.info(f"✅ RustFS保存成功: {rustfs_path}")
                    
                    # 验证RustFS保存
                    try:
                        saved_info = self.rustfs_storage.get_model_info(version)
                        if saved_info:
                            logger.info(f"✅ RustFS保存验证成功: 版本={version}")
                        else:
                            logger.error(f"❌ RustFS保存验证失败: 无法获取版本信息")
                    except Exception as e:
                        logger.error(f"❌ RustFS保存验证失败: {str(e)}")
                    
                except Exception as e:
                    logger.error(f"❌ RustFS保存失败: {str(e)}")
                    # 不抛出异常，继续使用本地存储
            else:
                logger.warning("⚠️ RustFS存储未启用或不可用")
            
            local_path = str(version_dir)
            logger.info(f"✅ 模型版本保存完成: {local_path}")
            return local_path
            
        except Exception as e:
            logger.exception(f"❌ 保存模型版本失败: {str(e)}")
            raise
    
    def list_models(self) -> List[Dict[str, Any]]:
        """
        列出所有模型版本
        """
        models = []
        
        # 从本地获取
        try:
            for version_dir in self.base_dir.iterdir():
                if version_dir.is_dir():
                    metadata_path = version_dir / "metadata.json"
                    if metadata_path.exists():
                        try:
                            with open(metadata_path, "r", encoding="utf-8") as f:
                                metadata = json.load(f)
                            models.append({
                                "version": version_dir.name,
                                "source": "local",
                                **metadata
                            })
                        except Exception as e:
                            logger.warning(f"读取本地元数据失败: {metadata_path}, {str(e)}")
        except Exception as e:
            logger.warning(f"获取本地模型列表失败: {str(e)}")
        
        # 从RustFS获取
        if self.use_rustfs and self.rustfs_storage:
            try:
                rustfs_models = self.rustfs_storage.list_models()
                existing_versions = {m.get("version") for m in models}
                for model in rustfs_models:
                    version = model.get("version")
                    if version not in existing_versions:
                        model["source"] = "rustfs"
                        models.append(model)
            except Exception as e:
                logger.warning(f"获取RustFS模型列表失败: {str(e)}")
        
        return models