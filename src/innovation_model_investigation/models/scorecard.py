"""
企业风险评分卡模型
基于逻辑回归的评分卡实现
"""

import pandas as pd
import numpy as np
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import cross_val_score
from typing import Dict, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class ScorecardModel:
    """企业风险评分卡模型"""
    
    def __init__(self, 
                 regularization: str = 'l1',
                 C: float = 1.0,
                 random_state: int = 42,
                 max_iter: int = 1000):
        """
        初始化评分卡模型
        
        Args:
            regularization: 正则化类型 ('l1', 'l2', 'elasticnet')
            C: 正则化强度的倒数
            random_state: 随机种子
            max_iter: 最大迭代次数
        """
        self.regularization = regularization
        self.C = C
        self.random_state = random_state
        self.max_iter = max_iter
        
        self.model = None
        self.scaler = None
        self.feature_names = None
        self.is_fitted = False
        
    def fit(self, X: pd.DataFrame, y: pd.Series) -> 'ScorecardModel':
        """
        训练评分卡模型
        
        Args:
            X: WOE编码后的特征矩阵
            y: 目标变量 (0=好企业, 1=坏企业)
            
        Returns:
            训练后的模型实例
        """
        logger.info("开始训练评分卡模型")
        
        # 保存特征名称
        self.feature_names = X.columns.tolist()
        
        # 标准化特征（可选，WOE已经标准化过了）
        self.scaler = StandardScaler()
        X_scaled = self.scaler.fit_transform(X)
        
        # 创建并训练逻辑回归模型
        self.model = LogisticRegression(
            penalty=self.regularization,
            C=self.C,
            random_state=self.random_state,
            max_iter=self.max_iter,
            solver='liblinear' if self.regularization == 'l1' else 'lbfgs'
        )
        
        self.model.fit(X_scaled, y)
        self.is_fitted = True
        
        logger.info("评分卡模型训练完成")
        
        return self
    
    def predict_proba(self, X: pd.DataFrame) -> np.ndarray:
        """
        预测概率
        
        Args:
            X: 特征矩阵
            
        Returns:
            预测概率数组 [好企业概率, 坏企业概率]
        """
        self._check_fitted()
        X_scaled = self.scaler.transform(X)
        return self.model.predict_proba(X_scaled)
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """
        预测类别
        
        Args:
            X: 特征矩阵
            
        Returns:
            预测类别数组
        """
        self._check_fitted()
        X_scaled = self.scaler.transform(X)
        return self.model.predict(X_scaled)
    
    def get_feature_weights(self) -> pd.DataFrame:
        """
        获取特征权重
        
        Returns:
            包含特征名称和权重的DataFrame
        """
        self._check_fitted()
        
        weights_df = pd.DataFrame({
            'feature_name': self.feature_names,
            'coefficient': self.model.coef_[0],
            'abs_coefficient': np.abs(self.model.coef_[0])
        })
        
        # 按绝对权重排序
        weights_df = weights_df.sort_values('abs_coefficient', ascending=False)
        weights_df['importance_rank'] = range(1, len(weights_df) + 1)
        
        return weights_df.reset_index(drop=True)
    
    def get_intercept(self) -> float:
        """获取模型截距"""
        self._check_fitted()
        return float(self.model.intercept_[0])
    
    def calculate_score(self, X: pd.DataFrame, base_score: int = 600, pdo: int = 20) -> pd.Series:
        """
        计算评分卡分数
        
        Args:
            X: 特征矩阵
            base_score: 基础分数
            pdo: 分数翻倍点 (Points to Double the Odds)
            
        Returns:
            评分卡分数序列
        """
        # 获取预测概率
        proba = self.predict_proba(X)[:, 1]  # 坏企业概率
        
        # 转换为评分
        odds = (1 - proba) / proba  # 好坏比
        factor = pdo / np.log(2)
        offset = base_score - factor * np.log(20)  # 假设基础odds=20
        
        scores = offset + factor * np.log(odds)
        
        return pd.Series(scores, index=X.index, name='scorecard_score')
    
    def cross_validate(self, X: pd.DataFrame, y: pd.Series, cv: int = 5) -> Dict:
        """
        交叉验证评估
        
        Args:
            X: 特征矩阵
            y: 目标变量
            cv: 交叉验证折数
            
        Returns:
            交叉验证结果字典
        """
        X_scaled = self.scaler.transform(X) if self.scaler else X
        
        # 准确率
        accuracy_scores = cross_val_score(
            self.model, X_scaled, y, cv=cv, scoring='accuracy'
        )
        
        # AUC
        auc_scores = cross_val_score(
            self.model, X_scaled, y, cv=cv, scoring='roc_auc'
        )
        
        # 精确率
        precision_scores = cross_val_score(
            self.model, X_scaled, y, cv=cv, scoring='precision'
        )
        
        # 召回率
        recall_scores = cross_val_score(
            self.model, X_scaled, y, cv=cv, scoring='recall'
        )
        
        results = {
            'accuracy': {
                'mean': float(accuracy_scores.mean()),
                'std': float(accuracy_scores.std()),
                'scores': accuracy_scores.tolist()
            },
            'auc': {
                'mean': float(auc_scores.mean()),
                'std': float(auc_scores.std()),
                'scores': auc_scores.tolist()
            },
            'precision': {
                'mean': float(precision_scores.mean()),
                'std': float(precision_scores.std()),
                'scores': precision_scores.tolist()
            },
            'recall': {
                'mean': float(recall_scores.mean()),
                'std': float(recall_scores.std()),
                'scores': recall_scores.tolist()
            }
        }
        
        return results
    
    def get_model_summary(self) -> Dict:
        """
        获取模型摘要信息
        
        Returns:
            模型摘要字典
        """
        self._check_fitted()
        
        weights_df = self.get_feature_weights()
        
        summary = {
            'model_params': {
                'regularization': self.regularization,
                'C': self.C,
                'n_features': len(self.feature_names)
            },
            'feature_importance': {
                'most_important': weights_df.iloc[0]['feature_name'],
                'max_weight': float(weights_df.iloc[0]['abs_coefficient']),
                'n_zero_weights': int((weights_df['abs_coefficient'] == 0).sum())
            },
            'intercept': self.get_intercept()
        }
        
        return summary
    
    def _check_fitted(self):
        """检查模型是否已训练"""
        if not self.is_fitted:
            raise ValueError("模型未训练，请先调用 fit 方法")
    
    def save_model(self, filepath: str):
        """保存模型到文件"""
        import pickle
        
        model_data = {
            'model': self.model,
            'scaler': self.scaler,
            'feature_names': self.feature_names,
            'regularization': self.regularization,
            'C': self.C,
            'random_state': self.random_state,
            'max_iter': self.max_iter,
            'is_fitted': self.is_fitted
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)
            
        logger.info(f"模型已保存到: {filepath}")
    
    def load_model(self, filepath: str):
        """从文件加载模型"""
        import pickle
        
        with open(filepath, 'rb') as f:
            model_data = pickle.load(f)
            
        self.model = model_data['model']
        self.scaler = model_data['scaler']
        self.feature_names = model_data['feature_names']
        self.regularization = model_data['regularization']
        self.C = model_data['C']
        self.random_state = model_data['random_state']
        self.max_iter = model_data['max_iter']
        self.is_fitted = model_data['is_fitted']
        
        logger.info(f"模型已加载自: {filepath}") 