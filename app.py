"""
AI训练引擎API服务
"""
import time
import uvicorn
from fastapi import FastAPI
from fastapi.responses import RedirectResponse
from loguru import logger

# 更新导入路径
from src.innovation_model_investigation.api.models.training import ErrorResponse
from src.innovation_model_investigation.api.routers import training, data, prediction

# 记录启动时间
start_time = time.time()

# 创建FastAPI应用
app = FastAPI(
    title="AI训练引擎API",
    description="企业风险评估模型训练和预测API服务",
    version="0.1.2",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 健康检查API
@app.get("/api/v1/health", tags=["系统"])
async def health_check():
    """
    健康检查API，返回服务的当前状态和资源使用情况
    """
    import psutil
    
    return {
        "status": "ok",
        "version": "0.1.2",
        "uptime": time.time() - start_time,
        "resources": {
            "cpu": psutil.cpu_percent(),
            "memory": psutil.virtual_memory().percent,
            "disk": psutil.disk_usage("/").percent,
        },
        "active_tasks": getattr(training, 'active_tasks', 0),
    }

# 包含路由器
app.include_router(training.router)
app.include_router(data.router)
app.include_router(prediction.router)

# 根路径重定向到文档
@app.get("/")
async def root():
    """重定向到API文档"""
    return RedirectResponse(url="/docs")

# 主函数
if __name__ == "__main__":
    uvicorn.run("app:app", host="0.0.0.0", port=8000, reload=True)
