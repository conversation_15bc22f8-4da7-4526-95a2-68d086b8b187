"""
数据API路由器
"""
from fastapi import APIRouter, HTTPException, Depends, Query
from loguru import logger

from api.utils.data_client import DataClient

# 创建路由器
router = APIRouter(
    prefix="/api/v1",
    tags=["数据"],
)

# 获取数据客户端实例
def get_data_client():
    """获取数据客户端实例"""
    return DataClient()

# 验证数据集API
@router.get("/datasets/{dataset_id}/validate")
async def validate_dataset(
    dataset_id: str,
    data_client: DataClient = Depends(get_data_client),
):
    """
    验证数据集API
    
    验证指定数据集的格式和完整性
    
    - **dataset_id**: 数据集ID
    """
    logger.info(f"验证数据集: dataset_id={dataset_id}")
    
    try:
        validation_result = await data_client.validate_dataset(dataset_id)
        return validation_result
    except Exception as e:
        logger.exception(f"验证数据集失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"验证数据集失败: {str(e)}")

# 获取数据集模式API
@router.get("/datasets/{dataset_id}/schema")
async def get_dataset_schema(
    dataset_id: str,
    data_client: DataClient = Depends(get_data_client),
):
    """
    获取数据集模式API
    
    获取指定数据集的列名和数据类型
    
    - **dataset_id**: 数据集ID
    """
    logger.info(f"获取数据集模式: dataset_id={dataset_id}")
    
    try:
        schema = await data_client.get_dataset_schema(dataset_id)
        return {"schema": schema}
    except Exception as e:
        logger.exception(f"获取数据集模式失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取数据集模式失败: {str(e)}")

# 获取数据集预览API
@router.get("/datasets/{dataset_id}/preview")
async def get_dataset_preview(
    dataset_id: str,
    limit: int = Query(100, ge=1, le=1000, description="预览行数"),
    data_client: DataClient = Depends(get_data_client),
):
    """
    获取数据集预览API
    
    获取指定数据集的前N行数据
    
    - **dataset_id**: 数据集ID
    - **limit**: 预览行数，默认100，最大1000
    """
    logger.info(f"获取数据集预览: dataset_id={dataset_id}, limit={limit}")
    
    try:
        df_batch, _ = await data_client.get_dataset_batch(dataset_id, page=1, limit=limit)
        
        # 将DataFrame转换为字典列表
        preview_data = df_batch.to_dict(orient="records")
        
        return {
            "columns": list(df_batch.columns),
            "data": preview_data,
            "total_rows": len(preview_data),
        }
    except Exception as e:
        logger.exception(f"获取数据集预览失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取数据集预览失败: {str(e)}")