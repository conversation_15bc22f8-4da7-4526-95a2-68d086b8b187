"""
测试 RustFS 存储功能
"""
import os
import sys
import unittest
from pathlib import Path
import tempfile
import shutil
import json
import joblib
import numpy as np
from sklearn.linear_model import LogisticRegression

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from api.utils.rustfs_storage import RustFSModelStorage, RustFSModelStorageAdapter


# 定义一个可序列化的 WOE 编码器类
class MockWoeEncoder:
    """模拟 WOE 编码器类，用于测试"""
    
    def transform(self, X):
        """模拟转换方法"""
        return X


def create_test_model():
    """创建测试模型"""
    # 创建一个简单的逻辑回归模型
    model = LogisticRegression()
    X = np.array([[1, 2], [3, 4], [5, 6], [7, 8]])
    y = np.array([0, 0, 1, 1])
    model.fit(X, y)
    
    # 创建一个简单的 WOE 编码器
    woe_encoder = MockWoeEncoder()
    
    return model, woe_encoder


def test_rustfs_storage():
    """测试 RustFS 存储功能"""
    print("开始测试 RustFS 存储功能...")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    models_dir = os.path.join(temp_dir, "models")
    
    try:
        # 创建 RustFS 存储实例
        storage = RustFSModelStorage(base_dir=models_dir)
        print(f"创建 RustFS 存储实例成功，存储目录：{models_dir}")
        
        # 创建测试模型
        model, woe_encoder = create_test_model()
        print("创建测试模型成功")
        
        # 保存模型到临时文件
        model_path = os.path.join(temp_dir, "model.pkl")
        woe_encoder_path = os.path.join(temp_dir, "woe_encoder.pkl")
        joblib.dump(model, model_path)
        joblib.dump(woe_encoder, woe_encoder_path)
        print(f"保存模型到临时文件成功：{model_path}, {woe_encoder_path}")
        
        # 使用 RustFS 存储保存模型
        version = "v1"
        files = {
            "model": Path(model_path),
            "woe_encoder": Path(woe_encoder_path)
        }
        metadata = {
            "created_at": "2025-07-17T10:00:00",
            "metrics": {
                "accuracy": 0.95,
                "precision": 0.92,
                "recall": 0.91
            },
            "parameters": {
                "C": 1.0,
                "max_iter": 100
            }
        }
        
        try:
            version_dir = storage.save_model(version, files, metadata)
            print(f"使用 RustFS 存储保存模型成功，模型目录：{version_dir}")
            
            # 列出所有模型版本
            models = storage.list_models()
            print(f"列出所有模型版本成功，共 {len(models)} 个版本")
            for model_info in models:
                print(f"  - 版本：{model_info['version']}, 是否最新：{model_info['is_latest']}")
            
            # 获取模型信息
            model_info = storage.get_model_info(version)
            print(f"获取模型信息成功：{model_info}")
            
            # 加载模型
            loaded_files = storage.load_model(version)
            print(f"加载模型成功，文件列表：{loaded_files}")
            
            # 测试适配器
            adapter = RustFSModelStorageAdapter(base_dir=models_dir)
            adapter_loaded_files = adapter.load_model(version)
            print(f"使用适配器加载模型成功，文件列表：{adapter_loaded_files}")
            
            # 删除模型
            # storage.delete_model(version)
            # print(f"删除模型版本成功：{version}")
            
            print("RustFS 存储功能测试完成！")
            return True
        except Exception as e:
            print(f"RustFS 操作失败：{str(e)}")
            # 继续测试本地文件系统功能
            print("测试本地文件系统功能...")
            
            # 使用本地文件系统保存模型
            version_dir = os.path.join(models_dir, "innovation-model", version)
            os.makedirs(version_dir, exist_ok=True)
            
            # 复制文件
            for file_type, file_path in files.items():
                target_path = os.path.join(version_dir, f"{file_type}.pkl")
                shutil.copy2(str(file_path), target_path)
            
            # 保存元数据
            metadata_path = os.path.join(version_dir, "metadata.json")
            with open(metadata_path, "w", encoding="utf-8") as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            
            print(f"使用本地文件系统保存模型成功，模型目录：{version_dir}")
            print("本地文件系统功能测试完成！")
            return True
    except Exception as e:
        print(f"测试失败：{str(e)}")
        return False
    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir)


if __name__ == "__main__":
    success = test_rustfs_storage()
    sys.exit(0 if success else 1)