"""
模型训练器
负责模型训练、验证、调参等任务
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import cross_val_score, GridSearchCV, StratifiedKFold
from sklearn.metrics import roc_auc_score, classification_report

logger = logging.getLogger(__name__)


class ModelTrainer:
    """模型训练器"""

    def __init__(self, config=None):
        """
        初始化模型训练器

        Args:
            config: 配置管理器实例，如果为None则使用默认配置
        """
        self.config = config
        self.model = None
        self.best_params = None
        self.cv_scores = None
        self.training_history = {}

        # 从配置中获取模型训练参数
        if self.config:
            self.model_config = self.config.get_model_config()
        else:
            # 默认配置
            self.model_config = {
                'algorithm': 'logistic_regression',
                'cv_folds': 5,
                'scoring': 'roc_auc',
                'param_grid': {
                    'C': [0.001, 0.01, 0.1, 1, 10, 100],
                    'penalty': ['l1', 'l2'],
                    'solver': ['liblinear', 'saga'],
                    'max_iter': [1000]
                },
                'class_weight': 'balanced'
            }
        
    def train_logistic_regression(self,
                                 X_train: pd.DataFrame,
                                 y_train: pd.Series,
                                 X_val: Optional[pd.DataFrame] = None,
                                 y_val: Optional[pd.Series] = None,
                                 param_grid: Optional[Dict] = None,
                                 cv_folds: Optional[int] = None,
                                 scoring: Optional[str] = None,
                                 random_state: Optional[int] = None,
                                 class_weight: Optional[str] = None) -> Dict[str, Any]:
        """
        训练逻辑回归模型

        Args:
            X_train: 训练特征
            y_train: 训练标签
            X_val: 验证特征（可选）
            y_val: 验证标签（可选）
            param_grid: 参数网格，如果为None则使用配置中的值
            cv_folds: 交叉验证折数，如果为None则使用配置中的值
            scoring: 评估指标，如果为None则使用配置中的值
            random_state: 随机种子，如果为None则使用配置中的值
            class_weight: 类权重，如果为None则使用配置中的值

        Returns:
            训练结果字典
        """
        logger.info("开始训练逻辑回归模型...")

        # 使用配置中的值或传入的参数
        param_grid = param_grid if param_grid is not None else self.model_config.get('param_grid', {
            'C': [0.001, 0.01, 0.1, 1, 10, 100],
            'penalty': ['l1', 'l2'],
            'solver': ['liblinear', 'saga'],
            'max_iter': [1000]
        })

        cv_folds = cv_folds if cv_folds is not None else self.model_config.get('cv_folds', 5)
        scoring = scoring if scoring is not None else self.model_config.get('scoring', 'roc_auc')

        # 获取随机种子
        if random_state is None and self.config:
            random_state = self.config.get('data.random_state', 42)
        elif random_state is None:
            random_state = 42

        # 获取类权重
        class_weight = class_weight if class_weight is not None else self.model_config.get('class_weight', 'balanced')

        # 创建逻辑回归模型
        base_model = LogisticRegression(random_state=random_state, class_weight=class_weight)
        
        # 网格搜索
        cv = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=random_state)
        
        grid_search = GridSearchCV(
            estimator=base_model,
            param_grid=param_grid,
            cv=cv,
            scoring=scoring,
            n_jobs=-1,
            verbose=1
        )
        
        # 训练模型
        grid_search.fit(X_train, y_train)
        
        # 保存最佳模型和参数
        self.model = grid_search.best_estimator_
        self.best_params = grid_search.best_params_
        
        # 交叉验证分数
        self.cv_scores = cross_val_score(
            self.model, X_train, y_train, cv=cv, scoring=scoring
        )
        
        # 训练历史
        self.training_history = {
            'best_score': grid_search.best_score_,
            'best_params': self.best_params,
            'cv_mean': self.cv_scores.mean(),
            'cv_std': self.cv_scores.std(),
            'all_results': grid_search.cv_results_
        }
        
        # 验证集评估（如果提供）
        validation_results = {}
        if X_val is not None and y_val is not None:
            y_val_pred = self.model.predict(X_val)
            y_val_proba = self.model.predict_proba(X_val)[:, 1]
            
            validation_results = {
                'val_auc': roc_auc_score(y_val, y_val_proba),
                'val_report': classification_report(y_val, y_val_pred, output_dict=True)
            }
        
        logger.info(f"模型训练完成，最佳CV分数: {grid_search.best_score_:.4f}")
        
        return {
            'model': self.model,
            'best_params': self.best_params,
            'cv_scores': self.cv_scores,
            'training_history': self.training_history,
            'validation_results': validation_results
        }
    
    def get_feature_weights(self) -> pd.DataFrame:
        """获取特征权重"""
        if self.model is None:
            raise ValueError("请先训练模型")
        
        if hasattr(self.model, 'coef_'):
            feature_names = getattr(self.model, 'feature_names_in_', None)
            if feature_names is None:
                feature_names = [f'feature_{i}' for i in range(len(self.model.coef_[0]))]
            
            weights_df = pd.DataFrame({
                'feature': feature_names,
                'weight': self.model.coef_[0],
                'abs_weight': np.abs(self.model.coef_[0])
            })
            
            weights_df = weights_df.sort_values('abs_weight', ascending=False)
            return weights_df
        else:
            raise ValueError("模型不支持特征权重获取")
    
    def get_training_summary(self) -> Dict[str, Any]:
        """获取训练摘要"""
        if not self.training_history:
            return {'error': '没有训练历史记录'}
        
        return {
            'model_type': type(self.model).__name__,
            'best_cv_score': self.training_history['best_score'],
            'cv_mean': self.training_history['cv_mean'],
            'cv_std': self.training_history['cv_std'],
            'best_params': self.best_params,
            'model_params': self.model.get_params() if self.model else {}
        } 