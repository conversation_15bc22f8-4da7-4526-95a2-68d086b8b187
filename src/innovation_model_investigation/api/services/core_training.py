"""
核心训练逻辑，基于 pipeline.py 的验证流程
"""
import os
import time
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
import pandas as pd
import joblib
from loguru import logger

# 导入训练相关模块
try:
    from innovation_model_investigation.pipeline import main as run_pipeline
    TRAINING_MODULES_AVAILABLE = True
except ImportError as e:
    logger.warning(f"无法导入训练模块: {str(e)}")
    TRAINING_MODULES_AVAILABLE = False


class CoreTrainingEngine:
    """核心训练引擎，基于 pipeline.py 的验证流程"""
    
    def __init__(self):
        pass
            
    async def execute_training_with_pipeline(self, output_dir: Path) -> Tuple[str, Dict[str, Any]]:
        """
        使用现有pipeline.py执行训练并保存到RustFS
        
        Args:
            output_dir: 输出目录
            
        Returns:
            版本号和训练结果
        """
        logger.info("🚀 开始执行pipeline训练流程")
        start_time = time.time()
        
        if not TRAINING_MODULES_AVAILABLE:
            raise RuntimeError("训练模块不可用")
            
        try:
            # 执行现有的pipeline流程
            logger.info("📊 执行pipeline.main()...")
            results = run_pipeline()
            
            # 从结果中提取版本信息
            pipeline_output_dir = results.get('output_dir', 'outputs/0.1.2')
            version = Path(pipeline_output_dir).name
            
            # 提取评估结果
            evaluation_results = results.get('evaluation_results', {})
            metrics = evaluation_results.get('classification_metrics', {})
            
            # 构建文件路径
            pipeline_dir = Path(pipeline_output_dir)
            files = {}
            
            # 查找所有模型文件
            models_dir = pipeline_dir / "models"
            if models_dir.exists():
                for pkl_file in models_dir.glob("*.pkl"):
                    file_type = pkl_file.stem
                    files[file_type] = pkl_file
            
            # 查找其他重要文件
            for subdir in ["feature", "evaluation", "binning"]:
                subdir_path = pipeline_dir / subdir
                if subdir_path.exists():
                    for pkl_file in subdir_path.glob("*.pkl"):
                        file_type = f"{subdir}_{pkl_file.stem}"
                        files[file_type] = pkl_file
            
            # 构建元数据
            metadata = {
                "version": version,
                "created_at": datetime.now().isoformat(),
                "training_time": time.time() - start_time,
                "metrics": metrics,
                "pipeline_output_dir": str(pipeline_dir),
                "evaluation_results": evaluation_results
            }
            
            # 构建结果
            result = {
                "version": version,
                "metrics": metrics,
                "training_time": time.time() - start_time,
                "output_dir": str(pipeline_dir),
                "files": files,
                "evaluation_results": evaluation_results,
                "metadata": metadata
            }
            
            logger.info(f"✅ Pipeline训练完成: 版本={version}, 找到{len(files)}个文件")
            return version, result
            
        except Exception as e:
            logger.exception(f"❌ Pipeline训练失败: {str(e)}")
            raise