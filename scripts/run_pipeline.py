#!/usr/bin/env python3
"""
企业风险评估模型权重优化 - 快速运行脚本
运行完整的WOE分箱 + 逻辑回归权重优化流水线
"""

import os
import sys
from pathlib import Path

def main():
    """主函数"""
    print("🚀 企业风险评估模型权重优化系统")
    print("=" * 50)
    
    # 检查虚拟环境
    if 'VIRTUAL_ENV' not in os.environ:
        print("⚠️  警告: 未检测到虚拟环境")
        print("建议执行: source .venv/bin/activate")
        print()
    
    # 检查数据文件
    data_path = Path("data/enterprise_risk_sample_data.csv")
    if not data_path.exists():
        print(f"❌ 数据文件未找到: {data_path}")
        print("请确保样本数据文件存在")
        return
    
    dict_path = Path("data/data_dictionary.csv")
    if not dict_path.exists():
        print(f"❌ 数据字典未找到: {dict_path}")
        print("请确保数据字典文件存在")
        return
    
    print("✅ 数据文件检查通过")
    print()
    
    # 运行完整流水线
    try:
        from innovation_model_investigation.pipeline import main as run_pipeline
        results = run_pipeline()
        
        print("\n🎯 运行总结:")
        print("-" * 30)
        print("✅ WOE分箱优化: 完成")
        print("✅ 特征权重学习: 完成") 
        print("✅ 模型训练评估: 完成")
        print("✅ 结果保存输出: 完成")
        
        # 显示关键结果
        auc = results['evaluation_results']['classification_metrics']['auc']
        ks = results['evaluation_results']['classification_metrics']['ks']
        
        print(f"\n📊 关键指标:")
        print(f"  AUC: {auc:.4f}")
        print(f"  KS:  {ks:.4f}")
        
        output_dir = results['output_dir']
        print(f"\n📁 输出文件:")
        print(f"  {output_dir}/models/scorecard_model.pkl    - 训练好的评分卡模型")
        print(f"  {output_dir}/models/woe_encoder.pkl        - WOE编码器")
        print(f"  {output_dir}/feature/feature_weights.csv    - 特征权重结果")
        print(f"  {output_dir}/evaluation/evaluation_results.json - 评估结果详情")
        
        print("\n🎉 任务执行成功!")
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装所需依赖:")
        print("pip install -e .")
        
    except Exception as e:
        print(f"❌ 执行错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 