"""
API服务，用于提供科创企业健康性评估模型的在线打分功能。
"""

import joblib
import pandas as pd
import numpy as np
import math
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import uvicorn
import logging

# --- 1. 日志和配置 ---
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- 2. 评分卡参数和模型加载 ---

# 这些参数应与模型训练时定义的评分卡转换逻辑一致
# BASE_SCORE: 基准分，例如，一个标准好客户的分数
# BASE_ODDS: 在基准分时，坏客户与好客户的比率 (P(bad)/P(good))
# PDO: Points to Double the Odds，即分数增加多少时，坏好比会翻倍
BASE_SCORE = 700
BASE_ODDS = 20  # 对应5%的坏客户率
PDO = 30

# 根据上述参数计算评分公式中的A和B (Score = A - B * ln(odds))
try:
    B = PDO / math.log(2)
    A = BASE_SCORE + B * math.log(BASE_ODDS)
except Exception as e:
    logger.error(f"无法计算评分卡参数A和B: {e}")
    A, B = 0, 0

# 模型和编码器路径
MODEL_PATH = "outputs/scorecard_model.pkl"
WOE_ENCODER_PATH = "outputs/woe_encoder.pkl"

# 在服务启动时加载模型
model = None
woe_encoder = None
try:
    model = joblib.load(MODEL_PATH)
    woe_encoder = joblib.load(WOE_ENCODER_PATH)
    logger.info("模型和WOE编码器加载成功。")
except FileNotFoundError:
    logger.error(f"模型文件未找到。请确保 '{MODEL_PATH}' 和 '{WOE_ENCODER_PATH}' 存在。")
except Exception as e:
    logger.error(f"加载模型时发生未知错误: {e}")


# --- 3. API数据模型定义 ---

# 定义API请求体的数据结构，确保传入的数据格式正确
class EnterpriseFeatures(BaseModel):
    financial_ratio_current: float
    financial_ratio_quick: float
    financial_ratio_debt: float
    financial_ratio_equity: float
    financial_ratio_profit: float
    financial_cash_flow: float
    financial_turnover_asset: float
    financial_turnover_inventory: float
    financial_growth_revenue: float
    financial_stability_ebitda: float
    operation_market_share: float
    operation_customer_concentration: float
    operation_supplier_relationship: float
    operation_product_diversity: float
    operation_innovation_capability: float
    operation_quality_management: float
    operation_brand_value: float
    operation_management_efficiency: float
    operation_employee_stability: float
    operation_digitalization_level: float
    external_industry_position: float
    external_policy_sensitivity: float
    external_market_volatility: float
    external_regulatory_compliance: float
    external_environmental_impact: float
    external_social_responsibility: float
    external_technology_adaptation: float
    external_economic_dependency: float
    external_geographic_risk: float
    external_competitive_pressure: float
    establish_years: int

# 定义API响应体的数据结构
class ScoringResponse(BaseModel):
    health_score: float
    is_healthy_probability: float
    model_version: str = "1.0"


# --- 4. FastAPI应用创建 ---

app = FastAPI(
    title="科创企业健康性评估API",
    description="接收企业指标分数，返回最终的健康性评分。",
    version="1.0.0"
)

@app.on_event("startup")
async def startup_event():
    """服务启动时的检查"""
    if model is None or woe_encoder is None:
        raise RuntimeError("模型未能成功加载，API无法启动。")
    logger.info("API服务已准备就绪。")

@app.post("/score", response_model=ScoringResponse)
async def get_score(features: EnterpriseFeatures):
    """
    接收企业指标，计算并返回科创企业健康性评分。

    - **输入**: 包含30多个企业指标分数的JSON对象。
    - **输出**: 包含最终`health_score`和`is_healthy_probability`的JSON对象。
    """
    try:
        # 1. 将Pydantic模型转换为Pandas DataFrame
        input_df = pd.DataFrame([features.dict()])

        # 2. 应用WOE转换
        # 确保输入特征的列名和顺序与训练时一致
        woe_transformed_df = woe_encoder.transform(input_df)

        # 3. 确保模型输入的特征与训练时完全一致
        model_features = model.feature_names_in_
        woe_transformed_df = woe_transformed_df[model_features]

        # 4. 预测概率
        # 假设模型训练的目标变量y中，1代表“健康”，0代表“不健康”
        # predict_proba返回的是 [P(y=0), P(y=1)]
        probabilities = model.predict_proba(woe_transformed_df)[0]
        prob_unhealthy = probabilities[0]
        prob_healthy = probabilities[1]

        # 5. 计算odds和最终分数
        # odds = P(不健康) / P(健康)
        # 添加一个极小值以防止除以零
        odds = prob_unhealthy / (prob_healthy + 1e-9)
        
        # Score = A - B * ln(odds)
        score = A - B * np.log(odds + 1e-9)
        
        # 将分数限制在合理范围内，例如300-900分
        final_score = round(np.clip(score, 300, 900))

        return {
            "health_score": final_score,
            "is_healthy_probability": prob_healthy
        }

    except KeyError as e:
        logger.error(f"特征不匹配错误: {e}")
        raise HTTPException(status_code=400, detail=f"输入数据中缺少或多出特征: {e}")
    except Exception as e:
        logger.error(f"评分时发生未知错误: {e}")
        raise HTTPException(status_code=500, detail=f"评分过程中发生内部错误: {e}")

@app.get("/", summary="API健康检查")
async def health_check():
    """一个简单的健康检查端点，用于确认API正在运行。"""
    return {"status": "ok", "message": "欢迎使用企业健康性评估API！"}

# --- 5. 启动服务 ---
if __name__ == "__main__":
    # 使用uvicorn来运行FastAPI应用
    # 在终端中运行: uvicorn serve:app --reload
    uvicorn.run(app, host="0.0.0.0", port=8000)
