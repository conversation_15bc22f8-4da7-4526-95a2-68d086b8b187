# AI训练引擎设计文档

## Overview

AI训练引擎是一个简化的Python服务，将现有的评分卡训练项目封装为两个核心API接口。该引擎专注于提供高效的模型训练和预测功能，与Web平台进行集成，为业务专家提供机器学习模型的训练和使用能力。

**核心设计理念**：
- **简化接口**：只提供训练和预测两个核心API接口
- **专注算法**：将复杂的工程实现交给Web平台，训练引擎专注于算法逻辑
- **松耦合集成**：通过标准化的API契约实现与Web平台的松耦合集成
- **本地存储**：使用本地文件系统存储模型文件，简化部署和管理

设计目标：
- 保持现有评分卡训练逻辑的完整性和准确性
- 提供简单明确的API接口，便于Web平台集成
- 确保训练和预测过程的可靠性和性能
- 支持模型文件的本地存储和访问
- 提供基本的健康检查和状态监控功能

## Architecture

### 系统架构

```mermaid
graph TB
    subgraph "Web平台"
        WebAPI[Web平台API]
        DataStorage[数据存储]
    end
    
    subgraph "AI训练引擎"
        APILayer[API接口层]
        TrainingService[训练服务]
        PredictionService[预测服务]
        ModelStorage[模型存储]
    end
    
    WebAPI -- "1. 调用训练API" --> APILayer
    APILayer -- "2. 请求训练数据" --> WebAPI
    WebAPI -- "3. 返回数据" --> APILayer
    APILayer -- "4. 执行训练" --> TrainingService
    TrainingService -- "5. 保存模型" --> ModelStorage
    APILayer -- "6. 返回结果" --> WebAPI
    
    WebAPI -- "1. 调用预测API" --> APILayer
    APILayer -- "2. 加载模型" --> ModelStorage
    APILayer -- "3. 执行预测" --> PredictionService
    APILayer -- "4. 返回结果" --> WebAPI
```

### 技术栈选择

**API框架**: FastAPI
- 理由：高性能、易于使用、自动生成API文档
- 支持异步处理和类型提示，提高代码质量和可维护性

**训练引擎**: 现有Python评分卡训练代码
- 理由：保持现有训练逻辑的一致性和准确性
- 通过适配器模式集成到API服务中

**数据处理**: Pandas, NumPy
- 理由：强大的数据处理和分析能力
- 与现有训练代码兼容

**存储方案**: 本地文件系统
- 理由：简化部署和配置，减少外部依赖
- 支持模型文件的版本管理和访问

**日志系统**: Loguru
- 理由：结构化日志记录，便于问题诊断和性能分析
- 支持多种输出格式和日志级别

## Components and Interfaces

### 1. API接口层

**职责**: 提供RESTful API接口，处理请求和响应

**核心接口**:
```python
@app.post("/api/v1/train")
async def train_model(request: TrainingRequest) -> TrainingResponse:
    """
    训练模型API
    
    参数:
    - training_set_id: 训练集ID
    - parameters: 训练参数
    
    返回:
    - model_id: 模型ID
    - version: 版本号
    - metrics: 性能指标
    """
    pass

@app.post("/api/v1/predict")
async def predict(request: PredictionRequest) -> PredictionResponse:
    """
    预测API
    
    参数:
    - model_id: 模型ID
    - data: 企业信息数据
    
    返回:
    - prediction: 预测结果
    - confidence: 置信度
    """
    pass

@app.get("/api/v1/health")
async def health_check() -> HealthResponse:
    """
    健康检查API
    
    返回:
    - status: 服务状态
    - version: API版本
    - resources: 资源使用情况
    """
    pass
```

**设计决策**:
- 使用Pydantic模型进行请求和响应验证
- 实现统一的错误处理和日志记录
- 支持API版本控制，便于未来扩展

### 2. 训练服务组件

**职责**: 执行模型训练逻辑，管理训练过程

**核心接口**:
```python
class TrainingService:
    async def fetch_training_data(self, training_set_id: str) -> pd.DataFrame:
        """从Web平台获取训练数据"""
        pass
    
    async def train_model(self, data: pd.DataFrame, parameters: Dict[str, Any]) -> ModelInfo:
        """执行模型训练"""
        pass
    
    async def save_model(self, model: Any, model_id: str) -> str:
        """保存模型到本地文件系统"""
        pass
    
    async def calculate_metrics(self, model: Any, test_data: pd.DataFrame) -> Dict[str, float]:
        """计算模型性能指标"""
        pass
```

**设计决策**:
- 使用适配器模式集成现有训练代码
- 实现训练过程的异常处理和恢复机制
- 支持训练参数的验证和默认值处理

### 3. 预测服务组件

**职责**: 加载模型并执行预测

**核心接口**:
```python
class PredictionService:
    async def load_model(self, model_id: str) -> Any:
        """从本地文件系统加载模型"""
        pass
    
    async def predict(self, model: Any, data: Dict[str, Any]) -> PredictionResult:
        """执行预测"""
        pass
    
    async def calculate_confidence(self, prediction: Any) -> float:
        """计算预测结果的置信度"""
        pass
```

**设计决策**:
- 实现模型缓存机制，提高预测性能
- 支持批量预测和单条预测
- 提供预测结果的解释功能

### 4. 模型存储组件

**职责**: 管理模型文件的存储和访问

**核心接口**:
```python
class ModelStorage:
    async def save_model(self, model: Any, model_id: str, version: str) -> str:
        """保存模型到本地文件系统"""
        pass
    
    async def load_model(self, model_id: str, version: Optional[str] = None) -> Any:
        """加载模型"""
        pass
    
    async def list_models(self) -> List[ModelInfo]:
        """列出所有可用模型"""
        pass
    
    async def get_model_info(self, model_id: str) -> ModelInfo:
        """获取模型信息"""
        pass
```

**设计决策**:
- 使用目录结构组织模型文件，便于管理
- 实现模型文件的版本控制
- 支持模型元数据的存储和检索

## Data Models

### 核心数据模型

```python
# API请求和响应模型
class TrainingRequest(BaseModel):
    training_set_id: str
    parameters: Dict[str, Any] = {}
    callback_url: Optional[str] = None

class TrainingResponse(BaseModel):
    model_id: str
    version: str
    metrics: Dict[str, float]
    training_time: float
    status: str = "success"
    error: Optional[str] = None

class PredictionRequest(BaseModel):
    model_id: str
    data: Dict[str, Any]
    version: Optional[str] = None

class PredictionResponse(BaseModel):
    prediction: Any
    confidence: float
    explanation: Optional[Dict[str, Any]] = None
    status: str = "success"
    error: Optional[str] = None

class HealthResponse(BaseModel):
    status: str
    version: str
    uptime: float
    resources: Dict[str, float]
    active_tasks: int

# 内部数据模型
class ModelInfo(BaseModel):
    model_id: str
    version: str
    created_at: datetime
    parameters: Dict[str, Any]
    metrics: Dict[str, float]
    file_path: str
    size: int
    training_set_id: str
```

### 文件存储结构

```
models/
  ├── {model_id}/
  │     ├── {version}/
  │     │     ├── model.pkl       # 序列化的模型文件
  │     │     ├── metadata.json   # 模型元数据
  │     │     └── metrics.json    # 性能指标
  │     ├── latest -> {version}/  # 指向最新版本的符号链接
  │     └── versions.json         # 版本历史记录
  └── index.json                  # 模型索引
```

## Error Handling

### 错误分类和处理策略

**1. 用户输入错误**
- 参数验证失败：返回400状态码和详细的验证错误信息
- 资源不存在：返回404状态码和明确的错误消息
- 参数冲突：返回409状态码和冲突详情

**2. 系统运行错误**
- 训练失败：记录详细日志，返回500状态码和错误信息
- 模型加载失败：返回500状态码和具体错误原因
- 数据获取失败：返回502状态码和上游服务错误信息

**3. 资源限制错误**
- 系统资源不足：返回503状态码和资源限制信息
- 请求超时：返回504状态码和超时原因

### 错误响应格式

```python
class ErrorResponse(BaseModel):
    status: str = "error"
    error_code: str
    message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=datetime.now)
    request_id: str
```

## Testing Strategy

### 测试层次

**1. 单元测试**
- 覆盖所有核心业务逻辑
- 使用pytest框架
- 实现模拟对象替代外部依赖
- 目标代码覆盖率：90%以上

**2. 集成测试**
- 测试API接口的完整流程
- 验证与Web平台的数据交换
- 测试模型训练和预测的端到端流程

**3. 性能测试**
- 测试API接口的响应时间
- 验证并发请求处理能力
- 测试大数据集的训练性能

### 测试数据管理

- 使用固定的测试数据集
- 实现测试数据的自动生成
- 支持测试环境的数据隔离

## 部署和配置

### 部署选项

**1. 独立服务**
- 使用Gunicorn/Uvicorn作为WSGI/ASGI服务器
- 支持Docker容器化部署
- 配置Nginx作为反向代理

**2. 与Web平台集成**
- 作为Web平台的一个服务组件部署
- 共享存储和网络资源
- 通过内部网络通信

### 配置管理

**配置项**:
```python
class Settings(BaseSettings):
    # API配置
    api_version: str = "v1"
    api_prefix: str = "/api"
    debug: bool = False
    
    # 存储配置
    models_dir: str = "./models"
    
    # Web平台集成
    web_platform_url: str
    web_platform_api_key: str
    
    # 性能配置
    model_cache_size: int = 5
    max_workers: int = 4
    
    # 日志配置
    log_level: str = "INFO"
    log_file: Optional[str] = None
    
    class Config:
        env_file = ".env"
        env_prefix = "AI_ENGINE_"
```

## 安全考虑

**1. API安全**
- 实现API密钥认证
- 限制请求频率和并发数
- 验证所有输入数据

**2. 数据安全**
- 验证训练数据的完整性
- 保护模型文件的访问权限
- 实现敏感数据的脱敏处理

**3. 错误处理**
- 避免在错误响应中泄露敏感信息
- 实现详细的错误日志记录
- 提供适当的错误反馈