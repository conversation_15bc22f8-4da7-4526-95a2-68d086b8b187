"""
测试数据获取客户端
"""
import asyncio
import json
import pandas as pd
from api.utils.data_client import DataClient

async def test_data_client():
    """测试数据获取客户端"""
    print("开始测试数据获取客户端...")
    
    # 初始化数据客户端
    # 注意：这里使用了模拟的Web平台URL，实际使用时需要替换为真实的URL
    client = DataClient(base_url="http://localhost:3000", api_key="test_api_key")
    
    # 测试数据集ID
    dataset_id = "test_dataset_001"
    
    try:
        # 测试获取数据集
        print(f"\n1. 测试获取数据集: {dataset_id}")
        try:
            df = await client.get_dataset(dataset_id)
            print(f"成功获取数据集: {len(df)} 行, {len(df.columns)} 列")
            print(f"数据集列名: {list(df.columns)[:5]}...")
        except Exception as e:
            print(f"获取数据集失败: {str(e)}")
            # 使用本地数据作为备用
            print("使用本地数据作为备用...")
            df = pd.read_csv("data/enterprise_risk_sample_data.csv")
            print(f"成功加载本地数据: {len(df)} 行, {len(df.columns)} 列")
        
        # 测试分批获取数据集
        print(f"\n2. 测试分批获取数据集: {dataset_id}")
        try:
            df_batch, has_more = await client.get_dataset_batch(dataset_id, page=1, limit=10)
            print(f"成功获取数据集批次: {len(df_batch)} 行, {len(df_batch.columns)} 列, 是否有更多: {has_more}")
        except Exception as e:
            print(f"分批获取数据集失败: {str(e)}")
            # 使用本地数据作为备用
            print("使用本地数据作为备用...")
            df_batch = df.head(10)
            has_more = len(df) > 10
            print(f"成功加载本地数据批次: {len(df_batch)} 行, {len(df_batch.columns)} 列, 是否有更多: {has_more}")
        
        # 测试获取完整数据集
        print(f"\n3. 测试获取完整数据集: {dataset_id}")
        try:
            df_full = await client.get_full_dataset(dataset_id, batch_size=100)
            print(f"成功获取完整数据集: {len(df_full)} 行, {len(df_full.columns)} 列")
        except Exception as e:
            print(f"获取完整数据集失败: {str(e)}")
            # 使用本地数据作为备用
            print("使用本地数据作为备用...")
            df_full = df
            print(f"成功加载本地完整数据: {len(df_full)} 行, {len(df_full.columns)} 列")
        
        # 测试验证数据集
        print(f"\n4. 测试验证数据集: {dataset_id}")
        try:
            validation_result = await client.validate_dataset(dataset_id)
            print(f"数据集验证结果: {json.dumps(validation_result, indent=2)}")
        except Exception as e:
            print(f"验证数据集失败: {str(e)}")
            # 使用本地数据进行验证
            print("使用本地数据进行验证...")
            validation_result = {
                "is_valid": True,
                "row_count": len(df),
                "column_count": len(df.columns),
                "columns": list(df.columns),
                "missing_values": {},
                "errors": [],
            }
            print(f"本地数据验证结果: {json.dumps(validation_result, indent=2)}")
        
        # 测试获取数据集模式
        print(f"\n5. 测试获取数据集模式: {dataset_id}")
        try:
            schema = await client.get_dataset_schema(dataset_id)
            print(f"数据集模式: {json.dumps(dict(list(schema.items())[:5]), indent=2)}...")
        except Exception as e:
            print(f"获取数据集模式失败: {str(e)}")
            # 使用本地数据获取模式
            print("使用本地数据获取模式...")
            schema = {col: str(df[col].dtype) for col in df.columns}
            print(f"本地数据模式: {json.dumps(dict(list(schema.items())[:5]), indent=2)}...")
        
        print("\n测试完成!")
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_data_client())