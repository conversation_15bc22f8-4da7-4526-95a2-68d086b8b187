"""
模型评估指标计算
包括AUC、KS值、PSI等风控常用指标
"""

import pandas as pd
import numpy as np
from sklearn.metrics import roc_auc_score, roc_curve, precision_recall_curve
from sklearn.metrics import classification_report, confusion_matrix
from typing import Dict, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class ModelMetrics:
    """模型评估指标计算器"""
    
    @staticmethod
    def calculate_auc(y_true: np.ndarray, y_proba: np.ndarray) -> float:
        """
        计算AUC值
        
        Args:
            y_true: 真实标签
            y_proba: 预测概率
            
        Returns:
            AUC值
        """
        return float(roc_auc_score(y_true, y_proba))
    
    @staticmethod
    def calculate_ks(y_true: np.ndarray, y_proba: np.ndarray) -> Tuple[float, pd.DataFrame]:
        """
        计算KS值（Kolmogorov-Smirnov统计量）
        
        Args:
            y_true: 真实标签
            y_proba: 预测概率
            
        Returns:
            KS值和KS表格
        """
        # 创建KS表格
        df = pd.DataFrame({
            'y_true': y_true,
            'y_proba': y_proba
        })
        
        # 按概率降序排列
        df = df.sort_values('y_proba', ascending=False).reset_index(drop=True)
        
        # 计算累计分布
        total_bad = (df['y_true'] == 1).sum()
        total_good = (df['y_true'] == 0).sum()
        
        df['cum_bad'] = (df['y_true'] == 1).cumsum()
        df['cum_good'] = (df['y_true'] == 0).cumsum()
        
        df['bad_rate'] = df['cum_bad'] / total_bad
        df['good_rate'] = df['cum_good'] / total_good
        
        df['ks'] = np.abs(df['bad_rate'] - df['good_rate'])
        
        ks_value = df['ks'].max()
        
        return float(ks_value), df
    
    @staticmethod
    def calculate_psi(expected: np.ndarray, 
                     actual: np.ndarray, 
                     bins: int = 10) -> Tuple[float, pd.DataFrame]:
        """
        计算PSI值（Population Stability Index）
        
        Args:
            expected: 期望分布（训练集）
            actual: 实际分布（测试集）
            bins: 分箱数量
            
        Returns:
            PSI值和PSI表格
        """
        # 确定分箱边界（基于期望分布）
        _, bin_edges = np.histogram(expected, bins=bins)
        
        # 计算期望分布和实际分布在各分箱的占比
        expected_counts, _ = np.histogram(expected, bins=bin_edges)
        actual_counts, _ = np.histogram(actual, bins=bin_edges)
        
        expected_perc = expected_counts / len(expected)
        actual_perc = actual_counts / len(actual)
        
        # 避免除零错误
        expected_perc = np.where(expected_perc == 0, 0.0001, expected_perc)
        actual_perc = np.where(actual_perc == 0, 0.0001, actual_perc)
        
        # 计算PSI
        psi_values = (actual_perc - expected_perc) * np.log(actual_perc / expected_perc)
        psi = np.sum(psi_values)
        
        # 创建PSI表格
        psi_df = pd.DataFrame({
            'bin': range(len(bin_edges) - 1),
            'bin_left': bin_edges[:-1],
            'bin_right': bin_edges[1:],
            'expected_count': expected_counts,
            'actual_count': actual_counts,
            'expected_perc': expected_perc,
            'actual_perc': actual_perc,
            'psi_contribution': psi_values
        })
        
        return float(psi), psi_df
    
    @staticmethod
    def calculate_lift(y_true: np.ndarray, 
                      y_proba: np.ndarray, 
                      n_bins: int = 10) -> pd.DataFrame:
        """
        计算提升度表格
        
        Args:
            y_true: 真实标签
            y_proba: 预测概率
            n_bins: 分箱数量
            
        Returns:
            提升度表格
        """
        df = pd.DataFrame({
            'y_true': y_true,
            'y_proba': y_proba
        })
        
        # 按概率等频分箱
        df['bin'] = pd.qcut(df['y_proba'], q=n_bins, duplicates='drop')
        
        # 计算每个分箱的统计量
        lift_stats = df.groupby('bin').agg({
            'y_true': ['count', 'sum', 'mean'],
            'y_proba': 'mean'
        }).round(4)
        
        lift_stats.columns = ['total_count', 'bad_count', 'bad_rate', 'avg_proba']
        lift_stats.reset_index(inplace=True)
        
        # 计算好企业数量
        lift_stats['good_count'] = lift_stats['total_count'] - lift_stats['bad_count']
        
        # 计算整体坏账率
        overall_bad_rate = y_true.mean()
        
        # 计算提升度
        lift_stats['lift'] = lift_stats['bad_rate'] / overall_bad_rate
        
        # 重新排序列
        lift_stats = lift_stats[['bin', 'total_count', 'good_count', 'bad_count', 
                               'bad_rate', 'avg_proba', 'lift']]
        
        return lift_stats
    
    @staticmethod
    def comprehensive_evaluation(y_true: np.ndarray, 
                               y_pred: np.ndarray,
                               y_proba: np.ndarray) -> Dict:
        """
        综合模型评估
        
        Args:
            y_true: 真实标签
            y_pred: 预测标签
            y_proba: 预测概率
            
        Returns:
            综合评估结果字典
        """
        # 基本分类指标
        auc = ModelMetrics.calculate_auc(y_true, y_proba)
        ks_value, ks_table = ModelMetrics.calculate_ks(y_true, y_proba)
        
        # 混淆矩阵
        cm = confusion_matrix(y_true, y_pred)
        tn, fp, fn, tp = cm.ravel()
        
        # 计算各种率
        accuracy = (tp + tn) / (tp + tn + fp + fn)
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
        f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
        
        # ROC曲线数据
        fpr, tpr, thresholds = roc_curve(y_true, y_proba)
        
        # 找最优阈值（约登指数最大）
        youden_index = tpr - fpr
        optimal_idx = np.argmax(youden_index)
        optimal_threshold = thresholds[optimal_idx]
        
        # 提升度表格
        lift_table = ModelMetrics.calculate_lift(y_true, y_proba)
        
        results = {
            'classification_metrics': {
                'auc': float(auc),
                'ks': float(ks_value),
                'accuracy': float(accuracy),
                'precision': float(precision),
                'recall': float(recall),
                'specificity': float(specificity),
                'f1_score': float(f1_score)
            },
            'confusion_matrix': {
                'true_negative': int(tn),
                'false_positive': int(fp),
                'false_negative': int(fn),
                'true_positive': int(tp)
            },
            'optimal_threshold': float(optimal_threshold),
            'roc_data': {
                'fpr': fpr.tolist(),
                'tpr': tpr.tolist(),
                'thresholds': thresholds.tolist()
            },
            'lift_table': lift_table.to_dict('records'),
            'ks_table': ks_table.head(20).to_dict('records')  # 只保存前20行
        }
        
        return results
    
    @staticmethod
    def print_evaluation_summary(evaluation_results: Dict):
        """打印评估结果摘要"""
        metrics = evaluation_results['classification_metrics']
        cm = evaluation_results['confusion_matrix']
        
        print("=" * 50)
        print("📊 模型评估结果摘要")
        print("=" * 50)
        
        print("🎯 分类性能指标:")
        print(f"  AUC:        {metrics['auc']:.4f}")
        print(f"  KS值:       {metrics['ks']:.4f}")
        print(f"  准确率:     {metrics['accuracy']:.4f}")
        print(f"  精确率:     {metrics['precision']:.4f}")
        print(f"  召回率:     {metrics['recall']:.4f}")
        print(f"  特异性:     {metrics['specificity']:.4f}")
        print(f"  F1分数:     {metrics['f1_score']:.4f}")
        print()
        
        print("📈 混淆矩阵:")
        print(f"  真负例(TN): {cm['true_negative']}")
        print(f"  假正例(FP): {cm['false_positive']}")
        print(f"  假负例(FN): {cm['false_negative']}")
        print(f"  真正例(TP): {cm['true_positive']}")
        print()
        
        print(f"⚡ 最优阈值: {evaluation_results['optimal_threshold']:.4f}")
        print()
        
        # 性能判断
        print("🏆 性能评级:")
        if metrics['auc'] >= 0.8:
            auc_grade = "优秀"
        elif metrics['auc'] >= 0.7:
            auc_grade = "良好"
        elif metrics['auc'] >= 0.6:
            auc_grade = "一般"
        else:
            auc_grade = "较差"
            
        if metrics['ks'] >= 0.4:
            ks_grade = "优秀"
        elif metrics['ks'] >= 0.3:
            ks_grade = "良好"
        elif metrics['ks'] >= 0.2:
            ks_grade = "一般"
        else:
            ks_grade = "较差"
            
        print(f"  AUC评级:    {auc_grade}")
        print(f"  KS评级:     {ks_grade}")
        
        print("=" * 50) 