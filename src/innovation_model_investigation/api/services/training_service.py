"""
训练服务，集成pipeline和RustFS存储
"""
import time
import json
import uuid
from pathlib import Path
from typing import Dict, Any, Tuple
import pandas as pd
from loguru import logger

from innovation_model_investigation.api.services.core_training import CoreTrainingEngine
from innovation_model_investigation.api.utils.unified_storage import UnifiedModelStorage
from innovation_model_investigation.api.clients.data_client import DataClient


class TrainingService:
    """训练服务"""
    
    def __init__(self, models_dir: str = "outputs", web_platform_url: str = None, api_key: str = None):
        """
        初始化训练服务
        
        Args:
            models_dir: 模型存储目录
            web_platform_url: Web平台API基础URL
            api_key: API密钥
        """
        # 使用统一存储，确保RustFS保存
        self.unified_storage = UnifiedModelStorage(base_dir=models_dir, use_rustfs=True)
        
        # 初始化数据客户端
        self.data_client = DataClient(web_platform_url, api_key)
        
        # 创建输出目录
        self.output_dir = Path(models_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化核心训练引擎
        self.core_engine = CoreTrainingEngine()

    async def train_model(self, training_set_id: str, parameters: Dict[str, Any]) -> Tuple[str, Dict[str, Any]]:
        """
        执行模型训练（使用pipeline流程并保存到RustFS）
        
        Args:
            training_set_id: 训练集ID
            parameters: 训练参数
            
        Returns:
            模型ID和训练结果
        """
        logger.info(f"🚀 开始训练模型: 训练集ID={training_set_id}")
        start_time = time.time()
        
        try:
            # 使用核心训练引擎执行pipeline训练
            version, result = await self.core_engine.execute_training_with_pipeline(
                output_dir=self.output_dir
            )
            
            # 生成模型ID
            model_id = f"model_{uuid.uuid4().hex[:8]}"
            
            # 强制保存到统一存储（包括RustFS）
            logger.info("💾 开始保存到统一存储...")
            self.unified_storage.save_model_version(
                version=version,
                files=result["files"],
                metadata={
                    "model_id": model_id,
                    "training_set_id": training_set_id,
                    "parameters": parameters,
                    **result["metadata"]
                }
            )
            
            # 记录训练日志
            training_log = {
                "timestamp": time.time(),
                "training_set_id": training_set_id,
                "parameters": parameters,
                "model_id": model_id,
                "version": version,
                "metrics": result["metrics"],
                "training_time": result["training_time"],
            }
            
            # 确保日志目录存在
            log_dir = Path("logs/training")
            log_dir.mkdir(parents=True, exist_ok=True)
            
            # 写入日志文件
            log_file = log_dir / f"training_{time.strftime('%Y%m%d')}.jsonl"
            with open(log_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(training_log, ensure_ascii=False) + "\n")
            
            logger.info(f"✅ 模型训练完成: 模型ID={model_id}, 版本={version}, 耗时={result['training_time']:.2f}秒")
            
            return model_id, {
                "version": version,
                "metrics": result["metrics"],
                "training_time": result["training_time"],
            }
            
        except Exception as e:
            logger.exception(f"❌ 模型训练失败: {str(e)}")
            raise

    async def fetch_training_data(self, training_set_id: str) -> pd.DataFrame:
        """
        获取训练数据
        
        Args:
            training_set_id: 训练集ID
            
        Returns:
            训练数据DataFrame
        """
        try:
            # 尝试从Web平台获取数据
            data = await self.data_client.get_training_data(training_set_id)
            logger.info(f"📊 从Web平台获取训练数据: {len(data)} 行")
            return data
        except Exception as e:
            logger.warning(f"⚠️ 从Web平台获取数据失败: {str(e)}")
            
            # 回退到本地数据文件
            local_data_path = Path("data/enterprise_risk_sample_data.csv")
            if local_data_path.exists():
                data = pd.read_csv(local_data_path)
                logger.info(f"📊 使用本地数据文件: {len(data)} 行")
                return data
            else:
                raise FileNotFoundError("无法获取训练数据：Web平台不可用且本地数据文件不存在")

    def list_models(self) -> list:
        """
        列出所有模型
        
        Returns:
            模型列表
        """
        try:
            return self.unified_storage.list_models()
        except Exception as e:
            logger.exception(f"❌ 获取模型列表失败: {str(e)}")
            return []

    def get_model_info(self, model_id: str) -> Dict[str, Any]:
        """
        获取模型信息
        
        Args:
            model_id: 模型ID
            
        Returns:
            模型信息
        """
        try:
            # 从模型列表中查找对应的版本
            models = self.list_models()
            for model in models:
                if model.get("model_id") == model_id:
                    version = model.get("version")
                    return self.unified_storage.get_model_info(version)
            
            raise FileNotFoundError(f"模型不存在: {model_id}")
            
        except Exception as e:
            logger.exception(f"❌ 获取模型信息失败: {str(e)}")
            raise
