"""
存储工具类，用于管理模型文件的存储和访问
"""
import os
import json
import shutil
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime

from loguru import logger


class ModelStorage:
    """模型存储类，负责管理模型文件的存储和访问"""
    
    def __init__(self, base_dir: str = "models"):
        """
        初始化模型存储
        
        Args:
            base_dir: 模型存储基础目录
        """
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(parents=True, exist_ok=True)
        self.index_file = self.base_dir / "index.json"
        self._load_index()
    
    def _load_index(self):
        """加载模型索引文件"""
        if self.index_file.exists():
            with open(self.index_file, "r", encoding="utf-8") as f:
                self.index = json.load(f)
        else:
            self.index = {"models": {}}
            self._save_index()
    
    def _save_index(self):
        """保存模型索引文件"""
        with open(self.index_file, "w", encoding="utf-8") as f:
            json.dump(self.index, f, ensure_ascii=False, indent=2)
    
    def save_model(self, model_id: str, version: str, files: Dict[str, Path], metadata: Dict[str, Any]) -> str:
        """
        保存模型文件
        
        Args:
            model_id: 模型ID
            version: 版本号
            files: 文件路径字典，键为文件类型，值为文件路径
            metadata: 模型元数据
            
        Returns:
            模型目录路径
        """
        # 创建模型目录
        model_dir = self.base_dir / model_id / version
        model_dir.mkdir(parents=True, exist_ok=True)
        
        # 复制文件
        for file_type, file_path in files.items():
            if not file_path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            target_path = model_dir / f"{file_type}.pkl"
            shutil.copy2(file_path, target_path)
            logger.info(f"复制文件: {file_path} -> {target_path}")
        
        # 保存元数据
        metadata_path = model_dir / "metadata.json"
        with open(metadata_path, "w", encoding="utf-8") as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
        
        # 更新索引
        if model_id not in self.index["models"]:
            self.index["models"][model_id] = {
                "versions": [],
                "latest": version,
            }
        
        self.index["models"][model_id]["versions"].append(version)
        self.index["models"][model_id]["latest"] = version
        self._save_index()
        
        # 创建latest符号链接
        latest_link = self.base_dir / model_id / "latest"
        if os.path.exists(latest_link):
            os.remove(latest_link)
        os.symlink(version, latest_link, target_is_directory=True)
        
        return str(model_dir)
    
    def load_model(self, model_id: str, version: Optional[str] = None) -> Dict[str, Path]:
        """
        加载模型文件
        
        Args:
            model_id: 模型ID
            version: 版本号，不指定则使用最新版本
            
        Returns:
            文件路径字典
        """
        if model_id not in self.index["models"]:
            raise ValueError(f"模型不存在: {model_id}")
        
        if version is None:
            version = self.index["models"][model_id]["latest"]
        elif version not in self.index["models"][model_id]["versions"]:
            raise ValueError(f"模型版本不存在: {model_id}/{version}")
        
        model_dir = self.base_dir / model_id / version
        if not model_dir.exists():
            raise FileNotFoundError(f"模型目录不存在: {model_dir}")
        
        # 查找所有.pkl文件
        files = {}
        for file_path in model_dir.glob("*.pkl"):
            file_type = file_path.stem
            files[file_type] = file_path
        
        return files
    
    def get_model_info(self, model_id: str, version: Optional[str] = None) -> Dict[str, Any]:
        """
        获取模型信息
        
        Args:
            model_id: 模型ID
            version: 版本号，不指定则使用最新版本
            
        Returns:
            模型元数据
        """
        if model_id not in self.index["models"]:
            raise ValueError(f"模型不存在: {model_id}")
        
        if version is None:
            version = self.index["models"][model_id]["latest"]
        elif version not in self.index["models"][model_id]["versions"]:
            raise ValueError(f"模型版本不存在: {model_id}/{version}")
        
        metadata_path = self.base_dir / model_id / version / "metadata.json"
        if not metadata_path.exists():
            raise FileNotFoundError(f"模型元数据文件不存在: {metadata_path}")
        
        with open(metadata_path, "r", encoding="utf-8") as f:
            metadata = json.load(f)
        
        return metadata
    
    def list_models(self) -> List[Dict[str, Any]]:
        """
        列出所有模型
        
        Returns:
            模型列表
        """
        models = []
        for model_id, model_info in self.index["models"].items():
            try:
                metadata = self.get_model_info(model_id)
                models.append({
                    "model_id": model_id,
                    "latest_version": model_info["latest"],
                    "versions_count": len(model_info["versions"]),
                    "created_at": metadata.get("created_at"),
                    "metrics": metadata.get("metrics", {}),
                })
            except Exception as e:
                logger.error(f"获取模型信息失败: {model_id}, 错误: {str(e)}")
        
        return models
    
    def delete_model(self, model_id: str, version: Optional[str] = None) -> bool:
        """
        删除模型
        
        Args:
            model_id: 模型ID
            version: 版本号，不指定则删除所有版本
            
        Returns:
            是否删除成功
        """
        if model_id not in self.index["models"]:
            raise ValueError(f"模型不存在: {model_id}")
        
        if version is None:
            # 删除整个模型目录
            model_dir = self.base_dir / model_id
            if model_dir.exists():
                shutil.rmtree(model_dir)
            
            # 从索引中删除
            del self.index["models"][model_id]
            self._save_index()
            
            return True
        else:
            if version not in self.index["models"][model_id]["versions"]:
                raise ValueError(f"模型版本不存在: {model_id}/{version}")
            
            # 删除指定版本
            version_dir = self.base_dir / model_id / version
            if version_dir.exists():
                shutil.rmtree(version_dir)
            
            # 更新索引
            self.index["models"][model_id]["versions"].remove(version)
            
            # 如果删除的是最新版本，更新latest
            if self.index["models"][model_id]["latest"] == version:
                if self.index["models"][model_id]["versions"]:
                    # 使用最新的版本作为latest
                    self.index["models"][model_id]["latest"] = sorted(
                        self.index["models"][model_id]["versions"]
                    )[-1]
                    
                    # 更新latest符号链接
                    latest_link = self.base_dir / model_id / "latest"
                    if os.path.exists(latest_link):
                        os.remove(latest_link)
                    os.symlink(
                        self.index["models"][model_id]["latest"],
                        latest_link,
                        target_is_directory=True
                    )
                else:
                    # 如果没有版本了，删除整个模型
                    return self.delete_model(model_id)
            
            self._save_index()
            return True