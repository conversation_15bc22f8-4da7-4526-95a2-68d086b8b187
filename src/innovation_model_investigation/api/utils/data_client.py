"""
数据获取客户端，用于从Web平台获取训练数据
"""
import os
import json
import time
import requests
from typing import Dict, Any, List, Optional
import pandas as pd
from loguru import logger


class DataClient:
    """数据获取客户端，负责从Web平台获取训练数据"""
    
    def __init__(self, base_url: str = None, api_key: str = None):
        """
        初始化数据获取客户端
        
        Args:
            base_url: Web平台API基础URL，如果为None则从环境变量WEB_PLATFORM_URL获取
            api_key: API密钥，如果为None则从环境变量WEB_PLATFORM_API_KEY获取
        """
        self.base_url = base_url or os.environ.get("WEB_PLATFORM_URL", "http://localhost:3000")
        self.api_key = api_key or os.environ.get("WEB_PLATFORM_API_KEY", "")
        
        # 移除URL末尾的斜杠
        if self.base_url.endswith("/"):
            self.base_url = self.base_url[:-1]
        
        logger.info(f"初始化数据获取客户端: base_url={self.base_url}")
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
        }
        
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"
        
        return headers
    
    async def get_dataset(self, dataset_id: str) -> pd.DataFrame:
        """
        获取数据集
        
        Args:
            dataset_id: 数据集ID
            
        Returns:
            数据集DataFrame
        """
        logger.info(f"获取数据集: dataset_id={dataset_id}")
        
        # 构建API URL
        url = f"{self.base_url}/api/v1/datasets/{dataset_id}/data"
        
        try:
            # 使用异步HTTP客户端发送请求
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=self._get_headers()) as response:
                    response.raise_for_status()  # 如果响应状态码不是200，抛出异常
                    data = await response.json()
            
            # 检查响应格式
            if not isinstance(data, dict) or "data" not in data:
                raise ValueError(f"响应格式错误: {data}")
            
            # 将JSON数据转换为DataFrame
            df = pd.DataFrame(data["data"])
            
            logger.info(f"成功获取数据集: {len(df)} 行, {len(df.columns)} 列")
            return df
            
        except aiohttp.ClientError as e:
            logger.error(f"请求数据集失败: {str(e)}")
            raise
        except ValueError as e:
            logger.error(f"解析数据集失败: {str(e)}")
            raise
        except Exception as e:
            logger.exception(f"获取数据集时发生未知错误: {str(e)}")
            raise
    
    async def get_dataset_batch(self, dataset_id: str, page: int = 1, limit: int = 1000) -> tuple:
        """
        分批获取数据集
        
        Args:
            dataset_id: 数据集ID
            page: 页码，从1开始
            limit: 每页数据量
            
        Returns:
            (DataFrame, bool): 数据集DataFrame和是否有更多数据
        """
        logger.info(f"分批获取数据集: dataset_id={dataset_id}, page={page}, limit={limit}")
        
        # 构建API URL
        url = f"{self.base_url}/api/v1/datasets/{dataset_id}/data?page={page}&limit={limit}"
        
        try:
            # 使用异步HTTP客户端发送请求
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=self._get_headers()) as response:
                    response.raise_for_status()  # 如果响应状态码不是200，抛出异常
                    data = await response.json()
            
            # 检查响应格式
            if not isinstance(data, dict) or "data" not in data:
                raise ValueError(f"响应格式错误: {data}")
            
            # 将JSON数据转换为DataFrame
            df = pd.DataFrame(data["data"])
            
            # 获取总页数
            total_pages = data.get("total_pages", 1)
            
            logger.info(f"成功获取数据集批次: {len(df)} 行, {len(df.columns)} 列, 页码 {page}/{total_pages}")
            
            # 返回DataFrame和是否有更多数据
            return df, page < total_pages
            
        except aiohttp.ClientError as e:
            logger.error(f"请求数据集批次失败: {str(e)}")
            raise
        except ValueError as e:
            logger.error(f"解析数据集批次失败: {str(e)}")
            raise
        except Exception as e:
            logger.exception(f"获取数据集批次时发生未知错误: {str(e)}")
            raise
    
    async def get_full_dataset(self, dataset_id: str, batch_size: int = 1000) -> pd.DataFrame:
        """
        获取完整数据集，自动处理分页
        
        Args:
            dataset_id: 数据集ID
            batch_size: 每批数据量
            
        Returns:
            完整数据集DataFrame
        """
        logger.info(f"获取完整数据集: dataset_id={dataset_id}, batch_size={batch_size}")
        
        all_data = []
        page = 1
        has_more = True
        
        while has_more:
            df_batch, has_more = await self.get_dataset_batch(dataset_id, page, batch_size)
            all_data.append(df_batch)
            page += 1
        
        # 合并所有批次的数据
        if all_data:
            df = pd.concat(all_data, ignore_index=True)
            logger.info(f"成功获取完整数据集: {len(df)} 行, {len(df.columns)} 列")
            return df
        else:
            logger.warning(f"数据集为空: dataset_id={dataset_id}")
            return pd.DataFrame()
    
    async def validate_dataset(self, dataset_id: str) -> Dict[str, Any]:
        """
        验证数据集
        
        Args:
            dataset_id: 数据集ID
            
        Returns:
            验证结果
        """
        logger.info(f"验证数据集: dataset_id={dataset_id}")
        
        try:
            # 获取数据集
            df = await self.get_dataset(dataset_id)
            
            # 验证数据集
            validation_result = {
                "is_valid": True,
                "row_count": len(df),
                "column_count": len(df.columns),
                "columns": list(df.columns),
                "missing_values": {},
                "errors": [],
            }
            
            # 检查必要的列
            required_columns = ["innovation_health_level", "comprehensive_score", "label"]
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                validation_result["is_valid"] = False
                validation_result["errors"].append(f"缺少必要的列: {', '.join(missing_columns)}")
            
            # 检查缺失值
            for col in df.columns:
                missing_count = df[col].isna().sum()
                if missing_count > 0:
                    validation_result["missing_values"][col] = int(missing_count)
            
            # 检查数据类型
            try:
                # 尝试将label列转换为数值型
                if "label" in df.columns:
                    df["label"] = pd.to_numeric(df["label"])
            except Exception as e:
                validation_result["is_valid"] = False
                validation_result["errors"].append(f"label列无法转换为数值型: {str(e)}")
            
            logger.info(f"数据集验证结果: {validation_result}")
            return validation_result
            
        except Exception as e:
            logger.exception(f"验证数据集失败: {str(e)}")
            return {
                "is_valid": False,
                "errors": [str(e)],
            }
    
    async def get_dataset_schema(self, dataset_id: str) -> Dict[str, str]:
        """
        获取数据集模式
        
        Args:
            dataset_id: 数据集ID
            
        Returns:
            数据集模式，键为列名，值为数据类型
        """
        logger.info(f"获取数据集模式: dataset_id={dataset_id}")
        
        try:
            # 获取数据集
            df = await self.get_dataset(dataset_id)
            
            # 获取数据类型
            schema = {}
            for col in df.columns:
                dtype = df[col].dtype
                schema[col] = str(dtype)
            
            logger.info(f"成功获取数据集模式: {len(schema)} 列")
            return schema
            
        except Exception as e:
            logger.exception(f"获取数据集模式失败: {str(e)}")
            raise