"""
预测API路由器
"""
from typing import Dict, Any, Optional, List
import os
from pathlib import Path

from fastapi import APIRouter, HTTPException, Depends, File, UploadFile, Form, Query, BackgroundTasks
from fastapi.responses import JSONResponse, FileResponse
from loguru import logger
import pandas as pd
import numpy as np
import joblib
import uuid
import json
import time

from api.models.training import PredictionRequest, PredictionResponse
from api.utils.storage import ModelStorage
from api.utils.rustfs_storage import RustFSModelStorageAdapter
from api.services.prediction_service import PredictionService

# 创建路由器
router = APIRouter(
    prefix="/api/v1",
    tags=["预测"],
)

# 定义常量
LOGS_PREDICTIONS_DIR = Path("logs/predictions")
USE_RUSTFS = os.environ.get("USE_RUSTFS", "false").lower() == "true"

# 获取模型存储实例
def get_model_storage():
    """获取模型存储实例"""
    if USE_RUSTFS:
        logger.info("使用 RustFS 模型存储")
        return RustFSModelStorageAdapter(base_dir="models")
    else:
        logger.info("使用标准模型存储")
        return ModelStorage(base_dir="models")

# 获取预测服务实例
def get_prediction_service():
    """获取预测服务实例"""
    model_storage = get_model_storage()
    return PredictionService(model_storage=model_storage, use_rustfs=USE_RUSTFS)

# 预测API
@router.post("/predict", response_model=PredictionResponse)
async def predict(
    request: PredictionRequest,
    prediction_service: PredictionService = Depends(get_prediction_service),
):
    """
    预测API
    
    接收模型ID和企业信息数据，执行预测，返回预测结果和置信度
    
    - **model_id**: 模型ID
    - **data**: 企业信息数据
    - **version**: 模型版本，不指定则使用最新版本
    """
    logger.info(f"收到预测请求: 模型ID={request.model_id}")
    
    try:
        # 使用预测服务执行预测
        result = await prediction_service.predict(
            model_id=request.model_id,
            data=request.data,
            version=request.version
        )
        
        # 记录预测日志
        prediction_log = {
            "timestamp": time.time(),
            "model_id": request.model_id,
            "version": request.version or "latest",
            "input_data": request.data,
            "prediction": result["prediction"],
            "confidence": result["confidence"],
            "probability_healthy": result["explanation"]["probability_healthy"],
            "probability_unhealthy": result["explanation"]["probability_unhealthy"],
            "score": result["explanation"].get("score"),
        }
        
        # 确保日志目录存在
        LOGS_PREDICTIONS_DIR.mkdir(parents=True, exist_ok=True)
        
        # 写入日志文件
        log_file = LOGS_PREDICTIONS_DIR / f"prediction_{time.strftime('%Y%m%d')}.jsonl"
        with open(log_file, "a", encoding="utf-8") as f:
            f.write(json.dumps(prediction_log, ensure_ascii=False) + "\n")
        
        return PredictionResponse(
            prediction=result["prediction"],
            confidence=result["confidence"],
            explanation=result["explanation"],
        )
    except ValueError as e:
        logger.error(f"预测请求验证失败: {str(e)}")
        return PredictionResponse(
            prediction="",
            confidence=0.0,
            status="error",
            error=str(e),
        )
    except Exception as e:
        logger.exception(f"预测失败: {str(e)}")
        return PredictionResponse(
            prediction="",
            confidence=0.0,
            status="error",
            error=str(e),
        )

# 批量预测API
@router.post("/predict/batch")
async def predict_batch(
    model_id: str,
    data: List[Dict[str, Any]],
    version: Optional[str] = None,
    prediction_service: PredictionService = Depends(get_prediction_service),
):
    """
    批量预测API
    
    接收模型ID和多条企业信息数据，执行批量预测，返回预测结果列表
    
    - **model_id**: 模型ID
    - **data**: 企业信息数据列表
    - **version**: 模型版本，不指定则使用最新版本
    """
    logger.info(f"收到批量预测请求: 模型ID={model_id}, 数据条数={len(data)}")
    
    try:
        # 使用预测服务执行批量预测
        results = await prediction_service.predict_batch(
            model_id=model_id,
            data=data,
            version=version
        )
        
        # 记录批量预测日志
        prediction_log = {
            "timestamp": time.time(),
            "model_id": model_id,
            "version": version or "latest",
            "batch_size": len(data),
            "results_summary": {
                "healthy_count": sum(1 for r in results if r["prediction"] == "健康"),
                "unhealthy_count": sum(1 for r in results if r["prediction"] == "不健康"),
            }
        }
        
        # 确保日志目录存在
        LOGS_PREDICTIONS_DIR.mkdir(parents=True, exist_ok=True)
        
        # 写入日志文件
        log_file = LOGS_PREDICTIONS_DIR / f"prediction_{time.strftime('%Y%m%d')}.jsonl"
        with open(log_file, "a", encoding="utf-8") as f:
            f.write(json.dumps(prediction_log, ensure_ascii=False) + "\n")
        
        return {
            "results": results,
            "count": len(results),
            "status": "success",
        }
    except ValueError as e:
        logger.error(f"批量预测请求验证失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.exception(f"批量预测失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# 文件批量预测API
@router.post("/predict/file")
async def predict_file(
    model_id: str = Form(...),
    file: UploadFile = File(...),
    version: Optional[str] = None,
    output_format: str = Form("json", enum=["json", "csv"]),
    background_tasks: BackgroundTasks = None,
    prediction_service: PredictionService = Depends(get_prediction_service),
):
    """
    文件批量预测API
    
    上传CSV文件，执行批量预测，返回预测结果文件
    
    - **model_id**: 模型ID
    - **file**: CSV文件
    - **version**: 模型版本，不指定则使用最新版本
    - **output_format**: 输出格式，支持json和csv，默认json
    """
    logger.info(f"收到文件批量预测请求: 模型ID={model_id}, 文件名={file.filename}")
    
    try:
        # 保存上传的文件
        temp_file = Path(f"temp/{uuid.uuid4()}.csv")
        temp_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(temp_file, "wb") as f:
            content = await file.read()
            f.write(content)
        
        # 读取CSV文件
        try:
            input_df = pd.read_csv(temp_file)
        except Exception as e:
            raise ValueError(f"CSV文件解析失败: {str(e)}")
        
        logger.info(f"成功读取CSV文件: {len(input_df)} 行, {len(input_df.columns)} 列")
        
        # 将DataFrame转换为字典列表
        data_list = input_df.to_dict(orient='records')
        
        # 使用预测服务执行批量预测
        results = await prediction_service.predict_batch(
            model_id=model_id,
            data=data_list,
            version=version
        )
        
        # 准备结果
        results_df = input_df.copy()
        
        # 将预测结果添加到DataFrame
        for i, result in enumerate(results):
            for key, value in result.items():
                results_df.loc[i, key] = value
                
        # 保存结果文件
        output_dir = Path("outputs/predictions")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = time.strftime("%Y%m%d%H%M%S")
        output_filename = f"prediction_{timestamp}"
        
        if output_format == "csv":
            output_file = output_dir / f"{output_filename}.csv"
            results_df.to_csv(output_file, index=False)
        else:  # json
            output_file = output_dir / f"{output_filename}.json"
            results_df.to_json(output_file, orient="records", force_ascii=False, indent=2)
        
        # 记录批量预测日志
        prediction_log = {
            "timestamp": time.time(),
            "model_id": model_id,
            "version": version or "latest",
            "file_name": file.filename,
            "batch_size": len(input_df),
            "output_file": str(output_file),
            "results_summary": {
                "healthy_count": int((results_df["prediction"] == "健康").sum()),
                "unhealthy_count": int((results_df["prediction"] == "不健康").sum()),
            }
        }
        
        # 确保日志目录存在
        LOGS_PREDICTIONS_DIR.mkdir(parents=True, exist_ok=True)
        
        # 写入日志文件
        log_file = LOGS_PREDICTIONS_DIR / f"prediction_{time.strftime('%Y%m%d')}.jsonl"
        with open(log_file, "a", encoding="utf-8") as f:
            f.write(json.dumps(prediction_log, ensure_ascii=False) + "\n")
        
        # 清理临时文件
        if temp_file.exists():
            os.remove(temp_file)
        
        # 返回结果文件
        return FileResponse(
            path=output_file,
            filename=output_file.name,
            media_type="application/octet-stream",
        )
    except ValueError as e:
        logger.error(f"文件批量预测请求验证失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.exception(f"文件批量预测失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# 模型缓存管理API
@router.get("/predict/cache")
async def get_cache_info(
    prediction_service: PredictionService = Depends(get_prediction_service),
):
    """
    模型缓存信息API
    
    获取模型缓存信息
    """
    logger.info("收到模型缓存信息请求")
    
    try:
        cache_info = prediction_service.get_cache_info()
        return {
            "cache_info": cache_info,
            "status": "success",
        }
    except Exception as e:
        logger.exception(f"获取模型缓存信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取模型缓存信息失败: {str(e)}")

# 清除模型缓存API
@router.post("/predict/cache/clear")
async def clear_cache(
    prediction_service: PredictionService = Depends(get_prediction_service),
):
    """
    清除模型缓存API
    
    清除模型缓存
    """
    logger.info("收到清除模型缓存请求")
    
    try:
        prediction_service.clear_cache()
        return {
            "message": "模型缓存已清除",
            "status": "success",
        }
    except Exception as e:
        logger.exception(f"清除模型缓存失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清除模型缓存失败: {str(e)}")

# 模型预测统计API
@router.get("/predict/stats")
async def get_prediction_stats(
    model_id: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
):
    """
    模型预测统计API
    
    获取模型预测统计信息
    
    - **model_id**: 模型ID，不指定则返回所有模型的统计信息
    - **start_date**: 开始日期，格式为YYYYMMDD
    - **end_date**: 结束日期，格式为YYYYMMDD
    """
    logger.info(f"收到模型预测统计请求: model_id={model_id}, start_date={start_date}, end_date={end_date}")
    
    try:
        # 确保日志目录存在
        if not LOGS_PREDICTIONS_DIR.exists():
            return {
                "stats": {},
                "total_predictions": 0,
                "message": "无预测记录"
            }
        
        # 获取日志文件列表
        log_files = list(LOGS_PREDICTIONS_DIR.glob("prediction_*.jsonl"))
        if not log_files:
            return {
                "stats": {},
                "total_predictions": 0,
                "message": "无预测记录"
            }
        
        # 过滤日期范围
        if start_date:
            log_files = [f for f in log_files if f.name >= f"prediction_{start_date}.jsonl"]
        if end_date:
            log_files = [f for f in log_files if f.name <= f"prediction_{end_date}.jsonl"]
        
        # 读取日志文件
        stats = {}
        total_predictions = 0
        
        for log_file in log_files:
            with open(log_file, "r", encoding="utf-8") as f:
                for line in f:
                    try:
                        log_entry = json.loads(line.strip())
                        
                        # 过滤模型ID
                        if model_id and log_entry.get("model_id") != model_id:
                            continue
                        
                        # 统计单条预测
                        if "prediction" in log_entry:
                            total_predictions += 1
                            
                            # 按模型ID统计
                            model_id_key = log_entry.get("model_id", "unknown")
                            if model_id_key not in stats:
                                stats[model_id_key] = {
                                    "total": 0,
                                    "healthy": 0,
                                    "unhealthy": 0,
                                    "versions": {}
                                }
                            
                            stats[model_id_key]["total"] += 1
                            
                            # 按预测结果统计
                            if log_entry.get("prediction") == "健康":
                                stats[model_id_key]["healthy"] += 1
                            else:
                                stats[model_id_key]["unhealthy"] += 1
                            
                            # 按版本统计
                            version = log_entry.get("version", "unknown")
                            if version not in stats[model_id_key]["versions"]:
                                stats[model_id_key]["versions"][version] = {
                                    "total": 0,
                                    "healthy": 0,
                                    "unhealthy": 0,
                                }
                            
                            stats[model_id_key]["versions"][version]["total"] += 1
                            
                            if log_entry.get("prediction") == "健康":
                                stats[model_id_key]["versions"][version]["healthy"] += 1
                            else:
                                stats[model_id_key]["versions"][version]["unhealthy"] += 1
                        
                        # 统计批量预测
                        elif "batch_size" in log_entry:
                            batch_size = log_entry.get("batch_size", 0)
                            total_predictions += batch_size
                            
                            # 按模型ID统计
                            model_id_key = log_entry.get("model_id", "unknown")
                            if model_id_key not in stats:
                                stats[model_id_key] = {
                                    "total": 0,
                                    "healthy": 0,
                                    "unhealthy": 0,
                                    "versions": {}
                                }
                            
                            stats[model_id_key]["total"] += batch_size
                            
                            # 按预测结果统计
                            results_summary = log_entry.get("results_summary", {})
                            healthy_count = results_summary.get("healthy_count", 0)
                            unhealthy_count = results_summary.get("unhealthy_count", 0)
                            
                            stats[model_id_key]["healthy"] += healthy_count
                            stats[model_id_key]["unhealthy"] += unhealthy_count
                            
                            # 按版本统计
                            version = log_entry.get("version", "unknown")
                            if version not in stats[model_id_key]["versions"]:
                                stats[model_id_key]["versions"][version] = {
                                    "total": 0,
                                    "healthy": 0,
                                    "unhealthy": 0,
                                }
                            
                            stats[model_id_key]["versions"][version]["total"] += batch_size
                            stats[model_id_key]["versions"][version]["healthy"] += healthy_count
                            stats[model_id_key]["versions"][version]["unhealthy"] += unhealthy_count
                    
                    except Exception as e:
                        logger.error(f"解析日志条目失败: {str(e)}")
                        continue
        
        return {
            "stats": stats,
            "total_predictions": total_predictions,
        }
    except Exception as e:
        logger.exception(f"获取模型预测统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取模型预测统计失败: {str(e)}")